import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useUser } from '@stackframe/stack';
import { queryKeys } from '@/lib/queries';

/**
 * Hook for smart background refresh strategy
 * Only refreshes data when it's likely to be stale based on user actions
 */
export function useSmartRefresh() {
  const user = useUser();
  const queryClient = useQueryClient();
  const lastActiveRef = useRef<number>(Date.now());
  const refreshTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!user?.id) return;

    // Track user activity
    const updateLastActive = () => {
      lastActiveRef.current = Date.now();
    };

    // Listen for user interactions that might indicate data changes
    const events = ['click', 'keydown', 'touchstart', 'mousemove'];
    events.forEach(event => {
      document.addEventListener(event, updateLastActive, { passive: true });
    });

    // Check for stale data periodically when user is active
    const checkForStaleData = () => {
      const now = Date.now();
      const timeSinceLastActive = now - lastActiveRef.current;

      // Only refresh if user has been active recently (within 5 minutes)
      if (timeSinceLastActive < 5 * 60 * 1000) {
        refreshStaleQueries();
      }

      // Schedule next check - increased to 10 minutes to reduce frequency
      refreshTimeoutRef.current = setTimeout(checkForStaleData, 10 * 60 * 1000); // Check every 10 minutes
    };

    // Start the periodic check - increased delay
    refreshTimeoutRef.current = setTimeout(checkForStaleData, 10 * 60 * 1000);

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, updateLastActive);
      });
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [user?.id, queryClient]);

  const refreshStaleQueries = () => {
    if (!user?.id) return;

    // Get all queries and check which ones are stale
    const queryCache = queryClient.getQueryCache();
    const allQueries = queryCache.getAll();

    allQueries.forEach(query => {
      const queryKey = query.queryKey[0] as string;
      const isStale = query.isStale();
      const lastUpdated = query.state.dataUpdatedAt;
      const now = Date.now();
      
      // Define refresh priorities based on data type
      const refreshPriorities = {
        // High priority - refresh if stale and older than 10 minutes
        high: ['taskCounts', 'userSettings'],
        // Medium priority - refresh if stale and older than 15 minutes  
        medium: ['lists', 'tasks'],
        // Low priority - refresh if stale and older than 30 minutes
        low: ['tags', 'taskTags', 'bulkTaskTags']
      };

      let shouldRefresh = false;
      const timeSinceUpdate = now - lastUpdated;

      if (refreshPriorities.high.includes(queryKey) && isStale && timeSinceUpdate > 10 * 60 * 1000) {
        shouldRefresh = true;
      } else if (refreshPriorities.medium.includes(queryKey) && isStale && timeSinceUpdate > 15 * 60 * 1000) {
        shouldRefresh = true;
      } else if (refreshPriorities.low.includes(queryKey) && isStale && timeSinceUpdate > 30 * 60 * 1000) {
        shouldRefresh = true;
      }

      if (shouldRefresh) {
        // Invalidate to trigger background refetch
        queryClient.invalidateQueries({ 
          queryKey: query.queryKey,
          refetchType: 'none' // Don't refetch immediately, just mark as stale
        });
        
        // Then refetch in background
        setTimeout(() => {
          queryClient.refetchQueries({ 
            queryKey: query.queryKey,
            type: 'active'
          });
        }, Math.random() * 1000); // Random delay to spread out requests
      }
    });
  };

  // Manual refresh function for explicit user actions
  const forceRefresh = (queryTypes?: string[]) => {
    if (!user?.id) return;

    const queriesToRefresh = queryTypes || ['lists', 'tasks', 'taskCounts', 'tags'];
    
    queriesToRefresh.forEach(queryType => {
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey[0] as string;
          return queryKey === queryType;
        }
      });
    });
  };

  return {
    forceRefresh,
    refreshStaleQueries
  };
}

/**
 * Hook for handling app visibility changes
 * Refreshes critical data when app becomes visible after being hidden
 */
export function useVisibilityRefresh() {
  const user = useUser();
  const queryClient = useQueryClient();
  const hiddenTimeRef = useRef<number | null>(null);

  useEffect(() => {
    if (!user?.id) return;

    const handleVisibilityChange = () => {
      if (document.hidden) {
        // App is being hidden
        hiddenTimeRef.current = Date.now();
      } else {
        // App is becoming visible
        const hiddenTime = hiddenTimeRef.current;
        if (hiddenTime) {
          const timeHidden = Date.now() - hiddenTime;
          
          // If app was hidden for more than 5 minutes, refresh critical data
          if (timeHidden > 5 * 60 * 1000) {
            refreshCriticalData();
          }
          
          hiddenTimeRef.current = null;
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [user?.id, queryClient]);

  const refreshCriticalData = () => {
    if (!user?.id) return;

    // Refresh only the most critical data that might have changed
    const criticalQueries = [
      queryKeys.taskCounts(user.id),
      queryKeys.lists(user.id)
    ];

    criticalQueries.forEach(queryKey => {
      queryClient.invalidateQueries({ queryKey });
    });
  };

  return {
    refreshCriticalData
  };
}

/**
 * Hook for handling network status changes
 * Refreshes data when network comes back online
 */
export function useNetworkRefresh() {
  const user = useUser();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!user?.id) return;

    const handleOnline = () => {
      // When network comes back, refresh all stale queries
      queryClient.invalidateQueries({
        predicate: (query) => query.isStale()
      });
    };

    window.addEventListener('online', handleOnline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
    };
  }, [user?.id, queryClient]);
}

/**
 * Combined hook that provides all smart refresh functionality
 */
export function useSmartRefreshStrategy() {
  const smartRefresh = useSmartRefresh();
  const visibilityRefresh = useVisibilityRefresh();
  
  // Enable network refresh
  useNetworkRefresh();

  return {
    ...smartRefresh,
    ...visibilityRefresh
  };
}
