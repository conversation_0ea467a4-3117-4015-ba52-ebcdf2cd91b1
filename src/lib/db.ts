import { eq, desc, asc, and, gt, inArray, sql as drizzleSql, or } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';
import { db, users, userSettings, spaces, lists, tasks, taskActivities, tags, taskTags, spaceCollaborators, getJWTAuthenticatedDb } from './db/index';
import type { User, UserSettings, Space, List, Task, TaskActivity, Tag, TaskTag, SpaceCollaborator } from './db/schema';
import { withUserContext } from './rls';

export type { User, UserSettings, Space, List, Task, TaskActivity, Tag, TaskTag, SpaceCollaborator };
export type TaskSortOption = 'position' | 'title' | 'title_desc' | 'due_date' | 'due_date_desc';

export async function getUserById(id: string): Promise<User | null> {
  try {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
}

export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const result = await db.select().from(users).where(eq(users.email, email)).limit(1);
    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
}

export async function createUser(user: Omit<User, 'created_at' | 'updated_at'>): Promise<User | null> {
  try {
    // Insert the user
    const newUser = await db.insert(users).values({
      id: user.id,
      email: user.email,
      name: user.name,
      avatar_url: user.avatar_url,
    }).returning();

    // Create default "Tasks" space for the user
    const defaultSpace = await createSpace(user.id, 'Tasks', 'Default space for all your tasks', '📋');

    // Create default settings for the user with the default space
    await db.insert(userSettings).values({
      user_id: user.id,
      default_space_id: defaultSpace?.id || null,
    });

    if (defaultSpace) {
      // Create default "To Do" list in the default space
      await createList(user.id, defaultSpace.id, 'To Do');
    }

    return newUser.length > 0 ? newUser[0] : null;
  } catch (error) {
    console.error('Error creating user:', error);
    return null;
  }
}

export async function updateUser(id: string, data: Partial<User>): Promise<User | null> {
  try {
    const { email, name, avatar_url } = data;

    // Build the update object with only the fields that are provided
    const updateData: Partial<User> = {};
    if (email !== undefined) updateData.email = email;
    if (name !== undefined) updateData.name = name;
    if (avatar_url !== undefined) updateData.avatar_url = avatar_url;

    // Add the updated_at timestamp
    updateData.updated_at = new Date();

    const updatedUser = await db.update(users)
      .set(updateData)
      .where(eq(users.id, id))
      .returning();

    return updatedUser.length > 0 ? updatedUser[0] : null;
  } catch (error) {
    console.error('Error updating user:', error);
    return null;
  }
}

export async function getUserSettings(userId: string): Promise<UserSettings | null> {
  try {
    // Use explicit WHERE clause instead of RLS for now (until auth.user_id() function is created)
    const settings = await db.select().from(userSettings)
      .where(eq(userSettings.user_id, userId))
      .limit(1);
    return settings.length > 0 ? settings[0] : null;
  } catch (error) {
    console.error('Error getting user settings:', error);
    return null;
  }
}

export async function updateUserSettings(
  userId: string,
  data: Partial<UserSettings>
): Promise<UserSettings | null> {
  try {
    // Use explicit WHERE clause instead of RLS for now (until auth.user_id() function is created)
    const { theme, notifications_enabled, week_starts_on, mascot, primary_color, default_space_id } = data;

    // Build the update object with only the fields that are provided
    const updateData: Partial<UserSettings> = {};
    if (theme !== undefined) updateData.theme = theme;
    if (notifications_enabled !== undefined) updateData.notifications_enabled = notifications_enabled;
    if (week_starts_on !== undefined) updateData.week_starts_on = week_starts_on;
    if (mascot !== undefined) updateData.mascot = mascot;
    if (primary_color !== undefined) updateData.primary_color = primary_color;
    if (default_space_id !== undefined) updateData.default_space_id = default_space_id;

    // Add the updated_at timestamp
    updateData.updated_at = new Date();

    const updatedSettings = await db.update(userSettings)
      .set(updateData)
      .where(eq(userSettings.user_id, userId))
      .returning();

    return updatedSettings.length > 0 ? updatedSettings[0] : null;
  } catch (error) {
    console.error('Error updating user settings:', error);
    return null;
  }
}

// List-related functions

export async function getListsByUserId(userId: string): Promise<List[]> {
  try {
    // Use RLS for user-specific data
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();
      // Explicitly filter by user_id for extra security
      const result = await authDb.select().from(lists)
        .where(eq(lists.user_id, userId))
        .orderBy(asc(lists.position), asc(lists.created_at));
      return result;
    });
  } catch (error) {
    console.error('Error getting lists by user ID:', error);
    return [];
  }
}

export async function getListsBySpaceId(userId: string, spaceId: string): Promise<List[]> {
  try {
    // Use RLS for user-specific data
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();
      // Filter by both user_id and space_id for security
      const result = await authDb.select().from(lists)
        .where(and(eq(lists.user_id, userId), eq(lists.space_id, spaceId)))
        .orderBy(asc(lists.position), asc(lists.created_at));
      return result;
    });
  } catch (error) {
    console.error('Error getting lists by space ID:', error);
    return [];
  }
}

export async function getListById(id: string): Promise<List | null> {
  try {
    // First get the list to determine the user ID for RLS context
    const result = await db.select().from(lists).where(eq(lists.id, id)).limit(1);
    if (result.length === 0) {
      return null;
    }

    const userId = result[0].user_id;

    // Use RLS to ensure the user can only access their own list
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();
      const rlsResult = await authDb.select().from(lists).where(eq(lists.id, id)).limit(1);
      return rlsResult.length > 0 ? rlsResult[0] : null;
    });
  } catch (error) {
    console.error('Error getting list by ID:', error);
    return null;
  }
}

// Space functions
export async function getOwnedSpacesByUserId(userId: string): Promise<Space[]> {
  try {
    const ownedSpaces = await db.select().from(spaces).where(eq(spaces.user_id, userId));
    return ownedSpaces;
  } catch (error) {
    console.error('Error getting owned spaces by user ID:', error);
    return [];
  }
}

export async function getSpacesByUserId(userId: string): Promise<Space[]> {
  try {
    // Get spaces owned by the user
    const ownedSpaces = await getOwnedSpacesByUserId(userId);

    // Get spaces shared with the user
    const sharedSpaces = await getSharedSpaces(userId);

    // Combine and deduplicate (in case user owns a space they're also invited to)
    const allSpaces = [...ownedSpaces];
    sharedSpaces.forEach(sharedSpace => {
      if (!allSpaces.find(space => space.id === sharedSpace.id)) {
        allSpaces.push(sharedSpace);
      }
    });

    return allSpaces;
  } catch (error) {
    console.error('Error getting spaces by user ID:', error);
    return [];
  }
}

export async function getSpaceById(spaceId: string): Promise<Space | null> {
  try {
    const space = await db.select().from(spaces).where(eq(spaces.id, spaceId)).limit(1);
    return space.length > 0 ? space[0] : null;
  } catch (error) {
    console.error('Error getting space by ID:', error);
    return null;
  }
}

export async function createSpace(userId: string, name: string, description?: string | null, icon?: string | null): Promise<Space | null> {
  try {
    const spaceId = uuidv4();
    const newSpace = await db.insert(spaces).values({
      id: spaceId,
      user_id: userId,
      name,
      description: description || null,
      icon: icon || null,
    }).returning();

    return newSpace.length > 0 ? newSpace[0] : null;
  } catch (error) {
    console.error('Error creating space:', error);
    return null;
  }
}

export async function updateSpace(spaceId: string, data: { name?: string; description?: string | null; icon?: string | null }): Promise<Space | null> {
  try {
    const updateData: Partial<Space> = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.icon !== undefined) updateData.icon = data.icon;

    // Add the updated_at timestamp
    updateData.updated_at = new Date();

    const updatedSpace = await db.update(spaces)
      .set(updateData)
      .where(eq(spaces.id, spaceId))
      .returning();

    return updatedSpace.length > 0 ? updatedSpace[0] : null;
  } catch (error) {
    console.error('Error updating space:', error);
    return null;
  }
}

export async function deleteSpace(spaceId: string, userId: string): Promise<boolean> {
  try {
    const result = await db.delete(spaces)
      .where(and(eq(spaces.id, spaceId), eq(spaces.user_id, userId)))
      .returning();

    return result.length > 0;
  } catch (error) {
    console.error('Error deleting space:', error);
    return false;
  }
}

// Space Collaborator functions
export async function addSpaceCollaborator(spaceId: string, email: string): Promise<SpaceCollaborator | null> {
  try {
    const collaboratorId = uuidv4();

    // Check if user exists with this email
    const existingUser = await getUserByEmail(email);

    const newCollaborator = await db.insert(spaceCollaborators).values({
      id: collaboratorId,
      space_id: spaceId,
      email: email.toLowerCase(),
      user_id: existingUser?.id || null,
    }).returning();

    return newCollaborator.length > 0 ? newCollaborator[0] : null;
  } catch (error) {
    console.error('Error adding space collaborator:', error);
    return null;
  }
}

export async function removeSpaceCollaborator(collaboratorId: string, spaceId: string): Promise<boolean> {
  try {
    const result = await db.delete(spaceCollaborators)
      .where(and(eq(spaceCollaborators.id, collaboratorId), eq(spaceCollaborators.space_id, spaceId)))
      .returning();

    return result.length > 0;
  } catch (error) {
    console.error('Error removing space collaborator:', error);
    return false;
  }
}

export async function getSpaceCollaborators(spaceId: string, userId: string): Promise<(SpaceCollaborator & { user?: { name: string | null; avatar_url: string | null } })[]> {
  try {
    // Temporarily use non-authenticated database to test basic functionality
    // TODO: Fix RLS policies and switch back to authenticated database
    const collaborators = await db.select({
      id: spaceCollaborators.id,
      space_id: spaceCollaborators.space_id,
      email: spaceCollaborators.email,
      user_id: spaceCollaborators.user_id,
      joined_at: spaceCollaborators.joined_at,
      created_at: spaceCollaborators.created_at,
      user_name: users.name,
      user_avatar_url: users.avatar_url,
    })
    .from(spaceCollaborators)
    .leftJoin(users, eq(spaceCollaborators.user_id, users.id))
    .where(eq(spaceCollaborators.space_id, spaceId));

    console.log("🔍 Raw collaborators data:", collaborators);

    const result = collaborators.map(collaborator => ({
      id: collaborator.id,
      space_id: collaborator.space_id,
      email: collaborator.email,
      user_id: collaborator.user_id,
      joined_at: collaborator.joined_at,
      created_at: collaborator.created_at,
      user: (collaborator.user_name || collaborator.user_avatar_url) ? {
        name: collaborator.user_name,
        avatar_url: collaborator.user_avatar_url,
      } : undefined
    }));

    console.log("🔍 Mapped collaborators result:", result);
    return result;
  } catch (error) {
    console.error('Error getting space collaborators:', error);
    return [];
  }
}

export async function markCollaboratorAsJoined(userId: string, spaceId: string): Promise<SpaceCollaborator | null> {
  try {
    // Get user email to find the collaboration record
    const user = await getUserById(userId);
    if (!user) return null;

    const result = await db.update(spaceCollaborators)
      .set({
        user_id: userId,
        joined_at: new Date()
      })
      .where(and(
        eq(spaceCollaborators.space_id, spaceId),
        eq(spaceCollaborators.email, user.email.toLowerCase())
      ))
      .returning();

    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error('Error marking collaborator as joined:', error);
    return null;
  }
}

export async function getSharedSpaces(userId: string): Promise<Space[]> {
  try {
    // Get user email to find spaces they're invited to
    const user = await getUserById(userId);
    if (!user) return [];

    // Find spaces where user is a collaborator (by email or user_id)
    const sharedSpaceIds = await db.select({ space_id: spaceCollaborators.space_id })
      .from(spaceCollaborators)
      .where(or(
        eq(spaceCollaborators.email, user.email.toLowerCase()),
        eq(spaceCollaborators.user_id, userId)
      ));

    if (sharedSpaceIds.length === 0) return [];

    // Get the actual space records
    const spaceIds = sharedSpaceIds.map(s => s.space_id);
    const sharedSpaces = await db.select().from(spaces)
      .where(inArray(spaces.id, spaceIds));

    return sharedSpaces;
  } catch (error) {
    console.error('Error getting shared spaces:', error);
    return [];
  }
}

export async function createList(userId: string, spaceId: string, name: string, color?: string | null, description?: string | null): Promise<List | null> {
  try {
    // Get the highest position for this user's lists in this space
    const positionResult = await db.select({
      max_position: drizzleSql<number>`MAX(${lists.position})`,
    }).from(lists).where(and(eq(lists.user_id, userId), eq(lists.space_id, spaceId)));

    const maxPosition = positionResult[0]?.max_position || 0;
    const position = maxPosition + 1;

    const listId = uuidv4();
    const newList = await db.insert(lists).values({
      id: listId,
      user_id: userId,
      space_id: spaceId,
      name,
      description: description || null,
      color: color || null,
      position,
    }).returning();

    return newList.length > 0 ? newList[0] : null;
  } catch (error) {
    console.error('Error creating list:', error);
    return null;
  }
}

export async function updateList(id: string, userId: string, data: { name?: string; description?: string | null; color?: string | null }): Promise<List | null> {
  try {
    const { name, description, color } = data;
    const updateData: any = { updated_at: new Date() };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (color !== undefined) updateData.color = color;

    const updatedList = await db.update(lists)
      .set(updateData)
      .where(and(eq(lists.id, id), eq(lists.user_id, userId)))
      .returning();

    return updatedList.length > 0 ? updatedList[0] : null;
  } catch (error) {
    console.error('Error updating list:', error);
    return null;
  }
}

export async function deleteList(id: string, userId: string): Promise<boolean> {
  try {
    const result = await db.delete(lists)
      .where(and(eq(lists.id, id), eq(lists.user_id, userId)))
      .returning();

    return result.length > 0;
  } catch (error) {
    console.error('Error deleting list:', error);
    return false;
  }
}

export async function reorderLists(userId: string, listIds: string[]): Promise<boolean> {
  try {
    // Update each list with its new position
    for (let i = 0; i < listIds.length; i++) {
      await db.update(lists)
        .set({ position: i + 1, updated_at: new Date() })
        .where(and(eq(lists.id, listIds[i]), eq(lists.user_id, userId)));
    }
    return true;
  } catch (error) {
    console.error('Error reordering lists:', error);
    return false;
  }
}

export async function getDefaultListForUser(userId: string): Promise<List | null> {
  try {
    // Use RLS for user-specific data
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();
      // Get the first list for the user (should be "To Do")
      // Explicitly filter by user_id for extra security
      const result = await authDb.select().from(lists)
        .where(eq(lists.user_id, userId))
        .orderBy(asc(lists.position), asc(lists.created_at))
        .limit(1);

      return result.length > 0 ? result[0] : null;
    });
  } catch (error) {
    console.error('Error getting default list for user:', error);
    return null;
  }
}

export async function getTaskCountsByUserId(userId: string): Promise<Record<string, number>> {
  try {
    // Use RLS for user-specific data
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();
      const result = await authDb.select({
        list_id: tasks.list_id,
        count: drizzleSql<number>`COUNT(*)`,
      })
      .from(tasks)
      .where(and(
        eq(tasks.user_id, userId),
        drizzleSql`${tasks.status} != 'completed'`
      ))
      .groupBy(tasks.list_id);

      const counts: Record<string, number> = {};
      result.forEach(row => {
        counts[row.list_id] = Number(row.count);
      });

      return counts;
    });
  } catch (error) {
    console.error('Error getting task counts by user ID:', error);
    return {};
  }
}

// Task-related functions

export async function getTasksByUserId(
  userId: string,
  sortBy: TaskSortOption = 'position'
): Promise<Task[]> {
  try {
    console.log("DB function: getTasksByUserId called with userId:", userId);

    // Use RLS for user-specific data
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Apply sorting based on the sortBy parameter
      let result;
      if (sortBy === 'title') {
        result = await authDb.select().from(tasks).orderBy(asc(tasks.title));
      } else if (sortBy === 'title_desc') {
        result = await authDb.select().from(tasks).orderBy(desc(tasks.title));
      } else if (sortBy === 'due_date') {
        // This is a bit more complex with Drizzle, we need to handle NULL values
        result = await authDb.select().from(tasks).orderBy(
          drizzleSql`CASE WHEN ${tasks.due_date} IS NULL THEN 1 ELSE 0 END`,
          asc(tasks.due_date)
        );
      } else if (sortBy === 'due_date_desc') {
        result = await authDb.select().from(tasks).orderBy(
          drizzleSql`CASE WHEN ${tasks.due_date} IS NULL THEN 1 ELSE 0 END`,
          desc(tasks.due_date)
        );
      } else {
        // Default to position
        result = await authDb.select().from(tasks).orderBy(asc(tasks.position));
      }

      console.log("DB function: tasks found:", result.length);
      return result;
    });
  } catch (error) {
    console.error('Error getting tasks by user ID:', error);
    return [];
  }
}

export async function getTasksByListId(
  listId: string,
  sortBy: TaskSortOption = 'position',
  requestingUserId?: string // Add optional user ID for validation
): Promise<Task[]> {
  try {
    console.log("DB function: getTasksByListId called with listId:", listId, "requestingUserId:", requestingUserId);

    // First get the list to determine the user ID for RLS context
    const list = await db.select().from(lists).where(eq(lists.id, listId)).limit(1);
    if (list.length === 0) {
      console.log("DB function: list not found");
      return [];
    }

    const listOwnerId = list[0].user_id;

    // Security check: if requestingUserId is provided, ensure it matches the list owner
    if (requestingUserId && requestingUserId !== listOwnerId) {
      console.warn("DB function: Security violation - user", requestingUserId, "attempted to access list owned by", listOwnerId);
      return [];
    }

    // Use RLS for user-specific data
    return await withUserContext(listOwnerId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Apply sorting based on the sortBy parameter
      let result;
      if (sortBy === 'title') {
        result = await authDb.select().from(tasks).where(eq(tasks.list_id, listId)).orderBy(asc(tasks.title));
      } else if (sortBy === 'title_desc') {
        result = await authDb.select().from(tasks).where(eq(tasks.list_id, listId)).orderBy(desc(tasks.title));
      } else if (sortBy === 'due_date') {
        result = await authDb.select().from(tasks).where(eq(tasks.list_id, listId)).orderBy(
          drizzleSql`CASE WHEN ${tasks.due_date} IS NULL THEN 1 ELSE 0 END`,
          asc(tasks.due_date)
        );
      } else if (sortBy === 'due_date_desc') {
        result = await authDb.select().from(tasks).where(eq(tasks.list_id, listId)).orderBy(
          drizzleSql`CASE WHEN ${tasks.due_date} IS NULL THEN 1 ELSE 0 END`,
          desc(tasks.due_date)
        );
      } else {
        // Default to position
        result = await authDb.select().from(tasks).where(eq(tasks.list_id, listId)).orderBy(asc(tasks.position));
      }

      console.log("DB function: tasks found:", result.length);
      return result;
    });
  } catch (error) {
    console.error('Error getting tasks by list ID:', error);
    return [];
  }
}

export async function getTasksWithSubtasksByListId(
  listId: string,
  sortBy: TaskSortOption = 'position',
  requestingUserId?: string
): Promise<Task[]> {
  try {
    console.log("DB function: getTasksWithSubtasksByListId called with listId:", listId);

    // Get all tasks for the list
    const allTasks = await getTasksByListId(listId, sortBy, requestingUserId);

    // Separate parent tasks and subtasks
    const parentTasks = allTasks.filter(task => !task.parent_task_id);
    const subtasks = allTasks.filter(task => task.parent_task_id);

    // Create a map of subtasks by parent ID for efficient lookup
    const subtasksByParent = new Map<string, Task[]>();
    subtasks.forEach(subtask => {
      if (subtask.parent_task_id) {
        if (!subtasksByParent.has(subtask.parent_task_id)) {
          subtasksByParent.set(subtask.parent_task_id, []);
        }
        subtasksByParent.get(subtask.parent_task_id)!.push(subtask);
      }
    });

    // Sort subtasks by position within each parent
    subtasksByParent.forEach(subtaskList => {
      subtaskList.sort((a, b) => a.position - b.position);
    });

    // Build the final list with parent tasks followed by their subtasks
    const result: Task[] = [];
    parentTasks.forEach(parentTask => {
      result.push(parentTask);
      const taskSubtasks = subtasksByParent.get(parentTask.id) || [];
      result.push(...taskSubtasks);
    });

    console.log("DB function: returning tasks with subtasks:", result.length);
    return result;
  } catch (error) {
    console.error('Error getting tasks with subtasks by list ID:', error);
    return [];
  }
}

export async function getTaskById(id: string, userId?: string): Promise<Task | null> {
  try {
    // If userId is provided, use RLS for security
    if (userId) {
      return await withUserContext(userId, async () => {
        const authDb = getJWTAuthenticatedDb();
        const result = await authDb.select().from(tasks).where(eq(tasks.id, id)).limit(1);
        return result.length > 0 ? result[0] : null;
      });
    }

    // Fallback for backward compatibility (should be avoided in production)
    console.warn("DB function: getTaskById called without userId - potential security risk");
    const result = await db.select().from(tasks).where(eq(tasks.id, id)).limit(1);
    return result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error('Error getting task by ID:', error);
    return null;
  }
}

export async function getTasksByTagId(
  tagId: string,
  userId: string,
  sortBy: TaskSortOption = 'position'
): Promise<Task[]> {
  try {
    console.log("DB function: getTasksByTagId called with tagId:", tagId, "userId:", userId);

    // Use RLS for user-specific data
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Join tasks with taskTags to get tasks that have the specified tag
      let query = authDb
        .select({
          id: tasks.id,
          user_id: tasks.user_id,
          list_id: tasks.list_id,
          parent_task_id: tasks.parent_task_id,
          title: tasks.title,
          description: tasks.description,
          status: tasks.status,
          position: tasks.position,
          due_date: tasks.due_date,
          created_at: tasks.created_at,
          updated_at: tasks.updated_at,
        })
        .from(tasks)
        .innerJoin(taskTags, eq(tasks.id, taskTags.task_id))
        .where(eq(taskTags.tag_id, tagId));

      // Apply sorting based on the sortBy parameter
      let result;
      if (sortBy === 'title') {
        result = await query.orderBy(asc(tasks.title));
      } else if (sortBy === 'title_desc') {
        result = await query.orderBy(desc(tasks.title));
      } else if (sortBy === 'due_date') {
        result = await query.orderBy(
          drizzleSql`CASE WHEN ${tasks.due_date} IS NULL THEN 1 ELSE 0 END`,
          asc(tasks.due_date)
        );
      } else if (sortBy === 'due_date_desc') {
        result = await query.orderBy(
          drizzleSql`CASE WHEN ${tasks.due_date} IS NULL THEN 1 ELSE 0 END`,
          desc(tasks.due_date)
        );
      } else {
        // Default to position
        result = await query.orderBy(asc(tasks.position));
      }

      console.log("DB function: tasks found for tag:", result.length);
      return result;
    });
  } catch (error) {
    console.error('Error getting tasks by tag ID:', error);
    return [];
  }
}

export async function createTask(
  userId: string,
  listId: string,
  data: {
    title: string;
    description?: string;
    due_date?: Date;
    status?: string;
    parent_task_id?: string;
  }
): Promise<Task | null> {
  try {
    console.log("DB function: createTask called with userId:", userId, "data:", data);

    // Get the highest position for this list's tasks
    const positionResult = await db.select({
      max_position: drizzleSql<number>`MAX(${tasks.position})`,
    }).from(tasks).where(eq(tasks.list_id, listId));

    // Set the new task's position to be one higher than the current max
    const position = (positionResult[0]?.max_position || 0) + 1;
    console.log("DB function: new task position will be:", position);

    const { title, description, due_date, status = 'todo', parent_task_id } = data;

    // Generate a new UUID for the task
    const taskId = uuidv4();

    // Insert the task
    const newTask = await db.insert(tasks).values({
      id: taskId,
      user_id: userId,
      list_id: listId,
      parent_task_id: parent_task_id || null,
      title,
      description: description || null,
      due_date: due_date || null,
      status,
      position,
    }).returning();

    console.log("DB function: created task:", newTask[0]);

    // Create a task activity record for the creation
    if (newTask.length > 0) {
      const task = newTask[0];
      await createTaskActivity(userId, task.id, 'created');
      return task;
    }

    return null;
  } catch (error) {
    console.error('Error creating task:', error);
    return null;
  }
}

// Subtask-specific functions
export async function createSubtask(
  userId: string,
  parentTaskId: string,
  data: {
    title: string;
    description?: string;
    due_date?: Date;
    status?: string;
  }
): Promise<Task | null> {
  try {
    console.log("DB function: createSubtask called with userId:", userId, "parentTaskId:", parentTaskId, "data:", data);

    // First, get the parent task to ensure it exists and get its list_id
    const parentTask = await db.select().from(tasks)
      .where(and(eq(tasks.id, parentTaskId), eq(tasks.user_id, userId)))
      .limit(1);

    if (parentTask.length === 0) {
      console.log("DB function: parent task not found");
      return null;
    }

    const parent = parentTask[0];

    // Ensure the parent task is not already a subtask (single-level hierarchy only)
    if (parent.parent_task_id) {
      console.log("DB function: cannot create subtask of a subtask");
      return null;
    }

    // Get the highest position for subtasks of this parent
    const positionResult = await db.select({
      max_position: drizzleSql<number>`MAX(${tasks.position})`,
    }).from(tasks).where(and(
      eq(tasks.parent_task_id, parentTaskId),
      eq(tasks.user_id, userId)
    ));

    // Set the new subtask's position to be one higher than the current max
    const position = (positionResult[0]?.max_position || 0) + 1;
    console.log("DB function: new subtask position will be:", position);

    // Create the subtask using the existing createTask function
    return await createTask(userId, parent.list_id, {
      ...data,
      parent_task_id: parentTaskId,
    });
  } catch (error) {
    console.error('Error creating subtask:', error);
    return null;
  }
}

export async function getSubtasksByParentId(parentTaskId: string, userId: string): Promise<Task[]> {
  try {
    console.log("DB function: getSubtasksByParentId called with parentTaskId:", parentTaskId, "userId:", userId);

    const subtasks = await db.select().from(tasks)
      .where(and(
        eq(tasks.parent_task_id, parentTaskId),
        eq(tasks.user_id, userId)
      ))
      .orderBy(asc(tasks.position));

    console.log("DB function: found subtasks:", subtasks);
    return subtasks;
  } catch (error) {
    console.error('Error getting subtasks:', error);
    return [];
  }
}

export async function updateTask(
  id: string,
  userId: string,
  data: Partial<Omit<Task, 'id' | 'user_id' | 'created_at' | 'updated_at'>>
): Promise<Task | null> {
  try {
    console.log("DB function: updateTask called with id:", id, "userId:", userId, "data:", data);

    // Get the current task to check if status is changing to 'completed'
    const currentTask = await db.select().from(tasks)
      .where(and(eq(tasks.id, id), eq(tasks.user_id, userId)))
      .limit(1);

    if (currentTask.length === 0) {
      return null;
    }

    const current = currentTask[0];
    const { title, description, due_date, status, position } = data;

    // If no fields to update, just return the current task
    if (Object.keys(data).length === 0) {
      return current;
    }

    // Build the update object with only the fields that are provided
    // Use 'in' operator to check if field was explicitly provided (even if undefined)
    const updateData: Partial<Task> = {};
    if ('title' in data) updateData.title = title;
    if ('description' in data) updateData.description = description;
    if ('due_date' in data) {
      // Convert null to null (for clearing), undefined to null (for clearing), keep Date as is
      updateData.due_date = due_date === undefined ? null : due_date;
      console.log("DB function: including due_date in update, value:", due_date);
      console.log("DB function: updateData.due_date will be:", updateData.due_date);
    }
    if ('status' in data) updateData.status = status;
    if ('position' in data) updateData.position = position;

    // Add the updated_at timestamp
    updateData.updated_at = new Date();

    const updatedTask = await db.update(tasks)
      .set(updateData)
      .where(and(eq(tasks.id, id), eq(tasks.user_id, userId)))
      .returning();

    console.log("DB function: updated task:", updatedTask?.[0]);

    // Create a task activity record if the task was marked as completed
    if (updatedTask && updatedTask.length > 0) {
      const updated = updatedTask[0];

      // If the status was changed to 'completed', record a completion activity
      if (status === 'completed' && current.status !== 'completed') {
        await createTaskActivity(userId, id, 'completed');
      }

      return updated;
    }

    return null;
  } catch (error) {
    console.error('Error updating task:', error);
    return null;
  }
}

export async function deleteTask(id: string, userId: string): Promise<boolean> {
  try {
    console.log("DB function: deleteTask called with id:", id, "userId:", userId);

    const result = await db.delete(tasks)
      .where(and(eq(tasks.id, id), eq(tasks.user_id, userId)))
      .returning({ id: tasks.id });

    console.log("DB function: delete result:", result);

    return result.length > 0;
  } catch (error) {
    console.error('Error deleting task:', error);
    return false;
  }
}

export async function duplicateTaskWithTags(originalTaskId: string, userId: string): Promise<Task | null> {
  try {
    console.log("DB function: duplicateTaskWithTags called with originalTaskId:", originalTaskId, "userId:", userId);

    // Get the original task
    const originalTask = await db.select().from(tasks)
      .where(and(eq(tasks.id, originalTaskId), eq(tasks.user_id, userId)))
      .limit(1);

    if (originalTask.length === 0) {
      console.log("DB function: original task not found");
      return null;
    }

    const original = originalTask[0];
    console.log("DB function: found original task:", original);

    // Get the original task's position to insert the duplicate right after it
    const originalPosition = original.position;

    // Shift all tasks with position > originalPosition by 1
    await db.update(tasks)
      .set({
        position: drizzleSql`${tasks.position} + 1`,
        updated_at: new Date()
      })
      .where(and(
        eq(tasks.list_id, original.list_id),
        eq(tasks.user_id, userId),
        gt(tasks.position, originalPosition)
      ));

    // Generate a new UUID for the duplicated task
    const duplicatedTaskId = uuidv4();

    // Create the duplicated task with status reset to 'todo'
    const duplicatedTask = await db.insert(tasks).values({
      id: duplicatedTaskId,
      user_id: userId,
      list_id: original.list_id,
      title: original.title,
      description: original.description,
      due_date: original.due_date,
      status: 'todo', // Reset completion status
      position: originalPosition + 1, // Insert right after original
    }).returning();

    if (duplicatedTask.length === 0) {
      console.log("DB function: failed to create duplicated task");
      return null;
    }

    const newTask = duplicatedTask[0];
    console.log("DB function: created duplicated task:", newTask);

    // Copy all tags from the original task to the duplicated task using RLS
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      const originalTaskTags = await authDb.select({ tag_id: taskTags.tag_id })
        .from(taskTags)
        .where(eq(taskTags.task_id, originalTaskId));

      console.log("DB function: found original task tags with RLS:", originalTaskTags);

      if (originalTaskTags.length > 0) {
        const tagRelations = originalTaskTags.map(tagRelation => ({
          id: uuidv4(),
          task_id: duplicatedTaskId,
          tag_id: tagRelation.tag_id,
        }));

        await authDb.insert(taskTags).values(tagRelations);
        console.log("DB function: copied tags to duplicated task with RLS");
      }

      // Create a task activity record for the duplication
      await createTaskActivity(userId, newTask.id, 'created');

      return newTask;
    });
  } catch (error) {
    console.error('Error duplicating task:', error);
    return null;
  }
}

export async function deleteCompletedTasksByUserId(userId: string): Promise<number> {
  try {
    console.log("DB function: deleteCompletedTasksByUserId called with userId:", userId);

    const result = await db.delete(tasks)
      .where(and(
        eq(tasks.user_id, userId),
        eq(tasks.status, 'completed')
      ))
      .returning({ id: tasks.id });

    console.log("DB function: deleted completed tasks count:", result.length);

    return result.length;
  } catch (error) {
    console.error('Error deleting completed tasks:', error);
    return 0;
  }
}

// Bulk operations for selection mode
export async function bulkDeleteTasks(taskIds: string[], userId: string): Promise<number> {
  try {
    console.log("DB function: bulkDeleteTasks called with taskIds:", taskIds, "userId:", userId);

    if (taskIds.length === 0) return 0;

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Delete tasks in bulk - RLS ensures only user's tasks are affected
      const result = await authDb.delete(tasks)
        .where(inArray(tasks.id, taskIds))
        .returning({ id: tasks.id });

      console.log("DB function: bulk deleted tasks count:", result.length);
      return result.length;
    });
  } catch (error) {
    console.error('Error bulk deleting tasks:', error);
    return 0;
  }
}

export async function bulkDuplicateTasksWithTags(taskIds: string[], userId: string, listId: string): Promise<Task[]> {
  try {
    console.log("DB function: bulkDuplicateTasksWithTags called with taskIds:", taskIds, "userId:", userId, "listId:", listId);

    if (taskIds.length === 0) return [];

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Get original tasks
      const originalTasks = await authDb.select()
        .from(tasks)
        .where(and(
          inArray(tasks.id, taskIds),
          eq(tasks.list_id, listId) // Ensure tasks are from the specified list
        ))
        .orderBy(asc(tasks.position));

      if (originalTasks.length === 0) {
        console.log("DB function: no tasks found to duplicate");
        return [];
      }

      // Get the highest position in the list to place duplicates at top
      const positionResult = await authDb.select({
        max_position: drizzleSql<number>`MAX(${tasks.position})`,
      }).from(tasks).where(eq(tasks.list_id, listId));

      let currentPosition = (positionResult[0]?.max_position || 0) + 1;
      const duplicatedTasks: Task[] = [];

      // Duplicate each task
      for (const originalTask of originalTasks) {
        const duplicatedTaskId = uuidv4();

        const duplicatedTask = await authDb.insert(tasks).values({
          id: duplicatedTaskId,
          user_id: userId,
          list_id: listId,
          title: originalTask.title,
          description: originalTask.description,
          due_date: originalTask.due_date,
          status: 'todo', // Reset completion status
          position: currentPosition++, // Place at top in order
        }).returning();

        if (duplicatedTask.length > 0) {
          const newTask = duplicatedTask[0];
          duplicatedTasks.push(newTask);

          // Copy tags from original task
          const originalTags = await authDb.select({ tag_id: taskTags.tag_id })
            .from(taskTags)
            .where(eq(taskTags.task_id, originalTask.id));

          if (originalTags.length > 0) {
            const tagValues = originalTags.map(tag => ({
              id: uuidv4(),
              task_id: newTask.id,
              tag_id: tag.tag_id,
            }));

            await authDb.insert(taskTags).values(tagValues);
          }

          // Create a task activity record for the duplication
          await createTaskActivity(userId, newTask.id, 'created');
        }
      }

      console.log("DB function: bulk duplicated tasks:", duplicatedTasks);
      return duplicatedTasks;
    });
  } catch (error) {
    console.error('Error bulk duplicating tasks:', error);
    return [];
  }
}

export async function bulkMoveTasksToListDb(taskIds: string[], userId: string, newListId: string): Promise<Task[]> {
  try {
    console.log("DB function: bulkMoveTasksToListDb called with taskIds:", taskIds, "userId:", userId, "newListId:", newListId);

    if (taskIds.length === 0) return [];

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Get original tasks to verify they exist and belong to user
      const originalTasks = await authDb.select()
        .from(tasks)
        .where(inArray(tasks.id, taskIds))
        .orderBy(asc(tasks.position));

      if (originalTasks.length === 0) {
        console.log("DB function: no tasks found to move");
        return [];
      }

      // Filter out tasks that are already in the target list
      const tasksToMove = originalTasks.filter(task => task.list_id !== newListId);

      if (tasksToMove.length === 0) {
        console.log("DB function: all tasks are already in the target list");
        return originalTasks.filter(task => task.list_id === newListId);
      }

      // Get the highest position in the destination list to place tasks at top
      const positionResult = await authDb.select({
        max_position: drizzleSql<number>`MAX(${tasks.position})`,
      }).from(tasks).where(eq(tasks.list_id, newListId));

      let currentPosition = (positionResult[0]?.max_position || 0) + 1;
      const movedTasks: Task[] = [];

      // Move each task
      for (const task of tasksToMove) {
        const updatedTask = await authDb.update(tasks)
          .set({
            list_id: newListId,
            position: currentPosition++,
            updated_at: new Date()
          })
          .where(eq(tasks.id, task.id))
          .returning();

        if (updatedTask.length > 0) {
          movedTasks.push(updatedTask[0]);
        }
      }

      console.log("DB function: bulk moved tasks:", movedTasks);
      return movedTasks;
    });
  } catch (error) {
    console.error('Error bulk moving tasks:', error);
    return [];
  }
}

export async function bulkAddTagToTasksDb(taskIds: string[], userId: string, tagId: string): Promise<boolean> {
  try {
    console.log("DB function: bulkAddTagToTasksDb called with taskIds:", taskIds, "userId:", userId, "tagId:", tagId);

    if (taskIds.length === 0) return false;

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Verify the tag belongs to the user
      const tagCheck = await authDb.select({ id: tags.id })
        .from(tags)
        .where(eq(tags.id, tagId))
        .limit(1);

      if (tagCheck.length === 0) {
        console.log("DB function: tag not found or doesn't belong to user");
        return false;
      }

      // Verify tasks belong to the user
      const taskCheck = await authDb.select({ id: tasks.id })
        .from(tasks)
        .where(inArray(tasks.id, taskIds));

      if (taskCheck.length === 0) {
        console.log("DB function: no valid tasks found");
        return false;
      }

      const validTaskIds = taskCheck.map(task => task.id);

      // Get existing task-tag relationships to avoid duplicates
      const existingRelations = await authDb.select({ task_id: taskTags.task_id })
        .from(taskTags)
        .where(and(
          inArray(taskTags.task_id, validTaskIds),
          eq(taskTags.tag_id, tagId)
        ));

      const existingTaskIds = new Set(existingRelations.map(rel => rel.task_id));
      const newTaskIds = validTaskIds.filter(taskId => !existingTaskIds.has(taskId));

      if (newTaskIds.length === 0) {
        console.log("DB function: all tasks already have this tag");
        return true;
      }

      // Create new task-tag relationships
      const tagValues = newTaskIds.map(taskId => ({
        id: uuidv4(),
        task_id: taskId,
        tag_id: tagId,
      }));

      await authDb.insert(taskTags).values(tagValues);

      console.log("DB function: bulk added tag to tasks, new relations:", newTaskIds.length);
      return true;
    });
  } catch (error) {
    console.error('Error bulk adding tag to tasks:', error);
    return false;
  }
}

export async function bulkUpdateTasksStatus(taskIds: string[], userId: string, status: string): Promise<Task[]> {
  try {
    console.log("DB function: bulkUpdateTasksStatus called with taskIds:", taskIds, "userId:", userId, "status:", status);

    if (taskIds.length === 0) return [];

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Verify tasks belong to the user and update their status
      const updatedTasks = await authDb.update(tasks)
        .set({
          status,
          updated_at: new Date()
        })
        .where(inArray(tasks.id, taskIds))
        .returning();

      console.log("DB function: bulk updated task statuses:", updatedTasks.length);
      return updatedTasks;
    });
  } catch (error) {
    console.error('Error bulk updating task statuses:', error);
    return [];
  }
}

export async function reorderTasks(
  userId: string,
  taskIds: string[]
): Promise<boolean> {
  try {
    console.log("DB function: reorderTasks called with userId:", userId, "taskIds:", taskIds);

    // Update each task's position based on its index in the taskIds array
    for (let i = 0; i < taskIds.length; i++) {
      await db.update(tasks)
        .set({
          position: i + 1,
          updated_at: new Date()
        })
        .where(and(eq(tasks.id, taskIds[i]), eq(tasks.user_id, userId)));
    }

    return true;
  } catch (error) {
    console.error('Error reordering tasks:', error);
    return false;
  }
}

export async function moveTaskToList(
  taskId: string,
  userId: string,
  newListId: string
): Promise<Task | null> {
  try {
    console.log("DB function: moveTaskToList called with taskId:", taskId, "userId:", userId, "newListId:", newListId);

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // First verify the task belongs to the user
      const currentTask = await authDb.select()
        .from(tasks)
        .where(eq(tasks.id, taskId))
        .limit(1);

      if (currentTask.length === 0) {
        console.log("DB function: task not found or not accessible to user");
        return null;
      }

      const task = currentTask[0];

      // Verify the new list belongs to the user
      const listCheck = await authDb.select({ id: lists.id })
        .from(lists)
        .where(eq(lists.id, newListId))
        .limit(1);

      if (listCheck.length === 0) {
        console.log("DB function: destination list not found or not accessible to user");
        return null;
      }

      // If task is already in the target list, no need to move
      if (task.list_id === newListId) {
        console.log("DB function: task is already in the target list");
        return task;
      }

      // Get the highest position in the destination list to place task at top
      const positionResult = await authDb.select({
        max_position: drizzleSql<number>`MAX(${tasks.position})`,
      }).from(tasks).where(eq(tasks.list_id, newListId));

      const maxPosition = positionResult[0]?.max_position || 0;
      const newPosition = maxPosition + 1;

      // Update the task with new list_id and position (move to top)
      const updatedTask = await authDb.update(tasks)
        .set({
          list_id: newListId,
          position: newPosition,
          updated_at: new Date()
        })
        .where(eq(tasks.id, taskId))
        .returning();

      console.log("DB function: moved task to new list:", updatedTask[0]);

      return updatedTask.length > 0 ? updatedTask[0] : null;
    });
  } catch (error) {
    console.error('Error moving task to list:', error);
    return null;
  }
}

// Task Activity functions

export async function createTaskActivity(
  userId: string,
  taskId: string,
  activityType: 'created' | 'completed'
): Promise<TaskActivity | null> {
  try {
    // Generate a new UUID for the activity
    const activityId = uuidv4();

    const activity = await db.insert(taskActivities).values({
      id: activityId,
      user_id: userId,
      task_id: taskId,
      activity_type: activityType,
    }).returning();

    return activity.length > 0 ? activity[0] : null;
  } catch (error) {
    console.error('Error creating task activity:', error);
    return null;
  }
}

export async function getRecentTaskActivities(
  userId: string,
  limit: number = 5,
  offset: number = 0
): Promise<TaskActivity[]> {
  try {
    // This is a bit more complex with Drizzle, we need to join the tables
    const activities = await db.select({
      id: taskActivities.id,
      user_id: taskActivities.user_id,
      task_id: taskActivities.task_id,
      activity_type: taskActivities.activity_type,
      created_at: taskActivities.created_at,
      task_title: tasks.title,
    })
    .from(taskActivities)
    .innerJoin(tasks, eq(taskActivities.task_id, tasks.id))
    .where(eq(taskActivities.user_id, userId))
    .orderBy(desc(taskActivities.created_at))
    .limit(limit)
    .offset(offset);

    return activities;
  } catch (error) {
    console.error('Error getting recent task activities:', error);
    return [];
  }
}

export async function getUpcomingTasksByDueDate(
  userId: string
): Promise<Task[]> {
  try {
    // Get incomplete tasks with due dates
    const result = await db.select()
      .from(tasks)
      .where(
        and(
          eq(tasks.user_id, userId),
          drizzleSql`${tasks.status} != 'completed'`,
          drizzleSql`${tasks.due_date} IS NOT NULL`
        )
      )
      .orderBy(asc(tasks.due_date));

    return result;
  } catch (error) {
    console.error('Error getting upcoming tasks by due date:', error);
    return [];
  }
}

export async function getUpcomingTasksWithListsByDueDate(
  userId: string
): Promise<any[]> {
  try {
    // Get incomplete tasks with due dates, joined with lists to get list color
    const result = await db.select({
      id: tasks.id,
      user_id: tasks.user_id,
      list_id: tasks.list_id,
      title: tasks.title,
      description: tasks.description,
      due_date: tasks.due_date,
      status: tasks.status,
      position: tasks.position,
      created_at: tasks.created_at,
      updated_at: tasks.updated_at,
      list_color: lists.color,
    })
      .from(tasks)
      .innerJoin(lists, eq(tasks.list_id, lists.id))
      .where(
        and(
          eq(tasks.user_id, userId),
          drizzleSql`${tasks.status} != 'completed'`,
          drizzleSql`${tasks.due_date} IS NOT NULL`
        )
      )
      .orderBy(asc(tasks.due_date));

    return result;
  } catch (error) {
    console.error('Error getting upcoming tasks with lists by due date:', error);
    return [];
  }
}

// Tag functions

export async function createTag(
  userId: string,
  name: string,
  color: string
): Promise<Tag | null> {
  try {
    console.log("DB function: createTag called with userId:", userId, "name:", name, "color:", color);

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Check if tag with this name already exists for the user (using RLS)
      const existingTag = await authDb.select()
        .from(tags)
        .where(eq(tags.name, name))
        .limit(1);

      if (existingTag.length > 0) {
        console.log("DB function: tag already exists:", existingTag[0]);
        return existingTag[0];
      }

      // Generate a new UUID for the tag
      const tagId = uuidv4();

      const newTag = await authDb.insert(tags).values({
        id: tagId,
        user_id: userId,
        name,
        color,
      }).returning();

      console.log("DB function: created tag with RLS:", newTag[0]);
      return newTag.length > 0 ? newTag[0] : null;
    });
  } catch (error) {
    console.error('Error creating tag:', error);
    return null;
  }
}

export async function getTagsByUserId(userId: string): Promise<Tag[]> {
  try {
    // Use RLS for user-specific data with explicit WHERE clause for extra security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();
      const result = await authDb.select()
        .from(tags)
        .where(eq(tags.user_id, userId))
        .orderBy(asc(tags.name));

      return result;
    });
  } catch (error) {
    console.error('Error getting tags by user ID:', error);
    return [];
  }
}

export async function searchTagsByName(userId: string, searchTerm: string): Promise<Tag[]> {
  try {
    console.log("DB function: searchTagsByName called with userId:", userId, "searchTerm:", searchTerm);

    // Use RLS for user-specific data with explicit WHERE clause for extra security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();
      const result = await authDb.select()
        .from(tags)
        .where(and(
          eq(tags.user_id, userId),
          drizzleSql`LOWER(${tags.name}) LIKE LOWER(${'%' + searchTerm + '%'})`
        ))
        .orderBy(asc(tags.name));

      console.log("DB function: found matching tags:", result);
      return result;
    });
  } catch (error) {
    console.error('Error searching tags by name:', error);
    return [];
  }
}

export async function updateTag(
  tagId: string,
  userId: string,
  data: { name?: string; color?: string }
): Promise<Tag | null> {
  try {
    console.log("DB function: updateTag called with tagId:", tagId, "userId:", userId, "data:", data);

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Check if tag exists and belongs to user (using RLS)
      const existingTag = await authDb.select()
        .from(tags)
        .where(eq(tags.id, tagId))
        .limit(1);

      if (existingTag.length === 0) {
        console.log("DB function: tag not found or doesn't belong to user");
        return null;
      }

      // If updating name, check for duplicates (using RLS)
      if (data.name && data.name !== existingTag[0].name) {
        const duplicateTag = await authDb.select()
          .from(tags)
          .where(and(
            eq(tags.name, data.name),
            // Exclude current tag from duplicate check
            drizzleSql`${tags.id} != ${tagId}`
          ))
          .limit(1);

        if (duplicateTag.length > 0) {
          console.log("DB function: tag name already exists");
          return null;
        }
      }

      // Build update object
      const updateData: Partial<Tag> = {
        updated_at: new Date(),
      };

      if (data.name !== undefined) updateData.name = data.name;
      if (data.color !== undefined) updateData.color = data.color;

      const updatedTag = await authDb.update(tags)
        .set(updateData)
        .where(eq(tags.id, tagId))
        .returning();

      console.log("DB function: updated tag with RLS:", updatedTag[0]);
      return updatedTag.length > 0 ? updatedTag[0] : null;
    });
  } catch (error) {
    console.error('Error updating tag:', error);
    return null;
  }
}

export async function deleteTag(tagId: string, userId: string): Promise<boolean> {
  try {
    console.log("DB function: deleteTag called with tagId:", tagId, "userId:", userId);

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      const result = await authDb.delete(tags)
        .where(eq(tags.id, tagId))
        .returning({ id: tags.id });

      console.log("DB function: delete tag result with RLS:", result);
      return result.length > 0;
    });
  } catch (error) {
    console.error('Error deleting tag:', error);
    return false;
  }
}

// Task-Tag relationship functions

export async function addTagToTask(taskId: string, tagId: string, userId: string): Promise<TaskTag | null> {
  try {
    console.log("DB function: addTagToTask called with taskId:", taskId, "tagId:", tagId, "userId:", userId);

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Verify task belongs to user
      const taskCheck = await authDb.select({ id: tasks.id })
        .from(tasks)
        .where(eq(tasks.id, taskId))
        .limit(1);

      if (taskCheck.length === 0) {
        console.log("DB function: task not found or not accessible to user");
        return null;
      }

      // Verify tag belongs to user
      const tagCheck = await authDb.select({ id: tags.id })
        .from(tags)
        .where(eq(tags.id, tagId))
        .limit(1);

      if (tagCheck.length === 0) {
        console.log("DB function: tag not found or not accessible to user");
        return null;
      }

      // Check if the relationship already exists
      const existingRelation = await authDb.select()
        .from(taskTags)
        .where(and(eq(taskTags.task_id, taskId), eq(taskTags.tag_id, tagId)))
        .limit(1);

      if (existingRelation.length > 0) {
        console.log("DB function: task-tag relationship already exists:", existingRelation[0]);
        return existingRelation[0];
      }

      // Generate a new UUID for the relationship
      const relationId = uuidv4();

      const newRelation = await authDb.insert(taskTags).values({
        id: relationId,
        task_id: taskId,
        tag_id: tagId,
      }).returning();

      console.log("DB function: created task-tag relationship with RLS:", newRelation[0]);
      return newRelation.length > 0 ? newRelation[0] : null;
    });
  } catch (error) {
    console.error('Error adding tag to task:', error);
    return null;
  }
}

export async function removeTagFromTask(taskId: string, tagId: string, userId: string): Promise<boolean> {
  try {
    console.log("DB function: removeTagFromTask called with taskId:", taskId, "tagId:", tagId, "userId:", userId);

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // Verify task belongs to user
      const taskCheck = await authDb.select({ id: tasks.id })
        .from(tasks)
        .where(eq(tasks.id, taskId))
        .limit(1);

      if (taskCheck.length === 0) {
        console.log("DB function: task not found or not accessible to user");
        return false;
      }

      const result = await authDb.delete(taskTags)
        .where(and(eq(taskTags.task_id, taskId), eq(taskTags.tag_id, tagId)))
        .returning({ id: taskTags.id });

      console.log("DB function: remove tag from task result with RLS:", result);
      return result.length > 0;
    });
  } catch (error) {
    console.error('Error removing tag from task:', error);
    return false;
  }
}

export async function getTagsForTask(taskId: string, userId?: string): Promise<Tag[]> {
  try {
    console.log("DB function: getTagsForTask called with taskId:", taskId, "userId:", userId);

    // If userId is provided, use RLS for security
    if (userId) {
      return await withUserContext(userId, async () => {
        const authDb = getJWTAuthenticatedDb();

        // First verify the task belongs to the user
        const taskCheck = await authDb.select({ id: tasks.id })
          .from(tasks)
          .where(eq(tasks.id, taskId))
          .limit(1);

        if (taskCheck.length === 0) {
          console.log("DB function: task not found or not accessible to user");
          return [];
        }

        // Get tags for the task using RLS-protected query
        const result = await authDb.select({
          id: tags.id,
          user_id: tags.user_id,
          name: tags.name,
          color: tags.color,
          created_at: tags.created_at,
          updated_at: tags.updated_at,
        })
        .from(taskTags)
        .innerJoin(tags, eq(taskTags.tag_id, tags.id))
        .where(eq(taskTags.task_id, taskId))
        .orderBy(asc(tags.name));

        console.log("DB function: found tags for task (RLS):", result);
        return result;
      });
    }

    // Fallback for backward compatibility (should be avoided in production)
    console.warn("DB function: getTagsForTask called without userId - potential security risk");
    const result = await db.select({
      id: tags.id,
      user_id: tags.user_id,
      name: tags.name,
      color: tags.color,
      created_at: tags.created_at,
      updated_at: tags.updated_at,
    })
    .from(taskTags)
    .innerJoin(tags, eq(taskTags.tag_id, tags.id))
    .where(eq(taskTags.task_id, taskId))
    .orderBy(asc(tags.name));

    console.log("DB function: found tags for task (non-RLS):", result);
    return result;
  } catch (error) {
    console.error('Error getting tags for task:', error);
    return [];
  }
}

// Bulk function to get tags for multiple tasks at once
export async function getBulkTagsForTasks(taskIds: string[], userId: string): Promise<Record<string, Tag[]>> {
  try {
    if (taskIds.length === 0) {
      return {};
    }

    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // First verify all tasks belong to the user
      const taskCheck = await authDb.select({ id: tasks.id })
        .from(tasks)
        .where(and(
          eq(tasks.user_id, userId),
          inArray(tasks.id, taskIds)
        ));

      const validTaskIds = taskCheck.map(t => t.id);

      if (validTaskIds.length === 0) {
        return {};
      }

      // Get all tags for all valid tasks in one query
      const result = await authDb.select({
        taskId: taskTags.task_id,
        tag: {
          id: tags.id,
          user_id: tags.user_id,
          name: tags.name,
          color: tags.color,
          created_at: tags.created_at,
          updated_at: tags.updated_at,
        }
      })
      .from(taskTags)
      .innerJoin(tags, eq(taskTags.tag_id, tags.id))
      .where(inArray(taskTags.task_id, validTaskIds))
      .orderBy(asc(tags.name));

      // Group tags by task ID
      const taskTagsMap: Record<string, Tag[]> = {};

      // Initialize all task IDs with empty arrays
      validTaskIds.forEach(taskId => {
        taskTagsMap[taskId] = [];
      });

      // Populate with actual tags
      result.forEach(({ taskId, tag }) => {
        if (!taskTagsMap[taskId]) {
          taskTagsMap[taskId] = [];
        }
        taskTagsMap[taskId].push(tag);
      });

      return taskTagsMap;
    });
  } catch (error) {
    console.error('Error getting bulk tags for tasks:', error);
    return {};
  }
}

export async function setTaskTags(taskId: string, tagIds: string[], userId: string): Promise<boolean> {
  try {

    // Use RLS for security
    return await withUserContext(userId, async () => {
      const authDb = getJWTAuthenticatedDb();

      // First verify the task belongs to the user
      const taskCheck = await authDb.select({ id: tasks.id })
        .from(tasks)
        .where(eq(tasks.id, taskId))
        .limit(1);

      if (taskCheck.length === 0) {
        return false;
      }

      // Verify all tags belong to the user
      if (tagIds.length > 0) {
        const tagCheck = await authDb.select({ id: tags.id })
          .from(tags)
          .where(and(
            eq(tags.user_id, userId),
            inArray(tags.id, tagIds)
          ));

        if (tagCheck.length !== tagIds.length) {
          return false;
        }
      }

      // Remove all existing tags for this task (RLS will ensure only user's task-tag relations are affected)
      await authDb.delete(taskTags)
        .where(eq(taskTags.task_id, taskId));

      // Add the new tags
      if (tagIds.length > 0) {
        const newRelations = tagIds.map(tagId => ({
          id: uuidv4(),
          task_id: taskId,
          tag_id: tagId,
        }));

        await authDb.insert(taskTags).values(newRelations);
      }

      return true;
    });
  } catch (error) {
    console.error('Error setting task tags:', error);
    return false;
  }
}
