"use server";

import { unstable_cache, revalidateTag } from "next/cache";
import { 
  SpaceCollaborator, 
  addSpaceCollaborator, 
  removeSpaceCollaborator, 
  getSpaceCollaborators, 
  markCollaboratorAsJoined,
  getSharedSpaces,
  getOwnedSpacesByUserId
} from "@/lib/db";

export const fetchSpaceCollaborators = unstable_cache(
  async (spaceId: string, userId: string): Promise<SpaceCollaborator[]> => {
    const collaborators = await getSpaceCollaborators(spaceId, userId);
    return collaborators;
  },
  ["space-collaborators-jwt"],
  {
    tags: ["space-collaborators"],
    revalidate: 600, // 10 minutes
  }
);

// Wrapper function to handle authentication
export async function fetchSpaceCollaboratorsWithAuth(spaceId: string): Promise<SpaceCollaborator[]> {
  try {
    // Get current user for RLS context
    const { stackServerApp } = await import("@/stack");
    const user = await stackServerApp.getUser();
    if (!user) {
      return [];
    }

    const result = await fetchSpaceCollaborators(spaceId, user.id);
    return result;
  } catch (error) {
    console.error("Error in fetchSpaceCollaboratorsWithAuth:", error);
    return [];
  }
}

export const fetchOwnedSpaces = unstable_cache(
  async (userId: string) => {
    console.log("Server action: fetchOwnedSpaces called with userId:", userId);
    const spaces = await getOwnedSpacesByUserId(userId);
    console.log("Server action: fetchOwnedSpaces returning:", spaces);
    return spaces;
  },
  ["owned-spaces-jwt"],
  {
    tags: ["spaces"],
    revalidate: 600, // 10 minutes
  }
);

export const fetchSharedSpaces = unstable_cache(
  async (userId: string) => {
    const spaces = await getSharedSpaces(userId);
    return spaces;
  },
  ["shared-spaces-jwt"],
  {
    tags: ["space-collaborators", "spaces"],
    revalidate: 600, // 10 minutes
  }
);

export async function addCollaborator(
  spaceId: string,
  email: string
): Promise<SpaceCollaborator | null> {
  console.log("Server action: addCollaborator called with spaceId:", spaceId, "email:", email);
  
  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new Error("Invalid email format");
  }

  const collaborator = await addSpaceCollaborator(spaceId, email);

  // Invalidate cache when collaborator is added
  revalidateTag("space-collaborators");
  revalidateTag("spaces"); // Also invalidate spaces cache as shared spaces may change

  console.log("Server action: addCollaborator returning:", collaborator);
  return collaborator;
}

export async function removeCollaborator(
  collaboratorId: string,
  spaceId: string
): Promise<boolean> {
  console.log("Server action: removeCollaborator called with collaboratorId:", collaboratorId, "spaceId:", spaceId);

  const success = await removeSpaceCollaborator(collaboratorId, spaceId);

  // Invalidate cache when collaborator is removed
  revalidateTag("space-collaborators");
  revalidateTag("spaces"); // Also invalidate spaces cache as shared spaces may change

  console.log("Server action: removeCollaborator returning:", success);
  return success;
}

export async function joinSpace(
  userId: string,
  spaceId: string
): Promise<SpaceCollaborator | null> {
  console.log("Server action: joinSpace called with userId:", userId, "spaceId:", spaceId);

  const collaborator = await markCollaboratorAsJoined(userId, spaceId);

  // Invalidate cache when user joins space
  revalidateTag("space-collaborators");
  revalidateTag("spaces"); // Also invalidate spaces cache as user's spaces may change

  console.log("Server action: joinSpace returning:", collaborator);
  return collaborator;
}
