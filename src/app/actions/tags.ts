"use server";

import { unstable_cache, revalidateTag } from "next/cache";
import { stackServerApp } from "@/stack";
import {
  Tag,
  Task,
  createTag,
  getTagsByUserId,
  searchTagsByName,
  updateTag,
  deleteTag,
  addTagToTask,
  removeTagFromTask,
  getTagsForTask,
  getBulkTagsForTasks,
  setTaskTags,
  getTaskById
} from "@/lib/db";

export async function fetchTags(userId: string): Promise<Tag[]> {
  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    return [];
  }

  const tags = await getTagsByUserId(userId);
  return tags;
}

export async function searchTags(userId: string, searchTerm: string): Promise<Tag[]> {
  console.log("Server action: searchTags called with userId:", userId, "searchTerm:", searchTerm);

  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    console.log("Server action: searchTags failed - unauthorized");
    return [];
  }

  const tags = await searchTagsByName(userId, searchTerm);
  console.log("Server action: searchTags returning:", tags);
  return tags;
}

export async function createNewTag(userId: string, name: string, color: string): Promise<Tag | null> {
  console.log("Server action: createNewTag called with userId:", userId, "name:", name, "color:", color);

  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    console.log("Server action: createNewTag failed - unauthorized");
    return null;
  }

  if (!name.trim()) {
    console.log("Server action: createNewTag failed - empty name");
    return null;
  }

  const tag = await createTag(userId, name.trim(), color);

  // Invalidate cache when tag is created
  revalidateTag("tags");

  console.log("Server action: createNewTag returning:", tag);
  return tag;
}

export async function editTag(
  tagId: string,
  userId: string,
  data: { name?: string; color?: string }
): Promise<Tag | null> {
  console.log("Server action: editTag called with tagId:", tagId, "userId:", userId, "data:", data);

  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    console.log("Server action: editTag failed - unauthorized");
    return null;
  }

  if (data.name !== undefined && !data.name.trim()) {
    console.log("Server action: editTag failed - empty name");
    return null;
  }

  const tag = await updateTag(tagId, userId, {
    ...data,
    name: data.name?.trim()
  });

  // Invalidate cache when tag is updated
  revalidateTag("tags");

  console.log("Server action: editTag returning:", tag);
  return tag;
}

export async function removeTag(tagId: string, userId: string): Promise<boolean> {
  console.log("Server action: removeTag called with tagId:", tagId, "userId:", userId);

  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    console.log("Server action: removeTag failed - unauthorized");
    return false;
  }

  const result = await deleteTag(tagId, userId);

  // Invalidate cache when tag is deleted
  revalidateTag("tags");

  console.log("Server action: removeTag returning:", result);
  return result;
}

export async function fetchTaskTags(taskId: string, userId: string): Promise<Tag[]> {
  console.log("Server action: fetchTaskTags called with taskId:", taskId, "userId:", userId);

  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    console.log("Server action: fetchTaskTags failed - unauthorized");
    return [];
  }

  const tags = await getTagsForTask(taskId, userId);
  console.log("Server action: fetchTaskTags returning:", tags);
  return tags;
}

// Bulk fetch tags for multiple tasks at once
export async function fetchBulkTaskTags(taskIds: string[], userId: string): Promise<Record<string, Tag[]>> {
  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    return {};
  }

  try {
    const bulkTags = await getBulkTagsForTasks(taskIds, userId);
    return bulkTags;
  } catch (error) {
    console.error("Error in fetchBulkTaskTags:", error);
    return {};
  }
}

export async function addTaskTag(taskId: string, tagId: string, userId: string): Promise<boolean> {
  console.log("Server action: addTaskTag called with taskId:", taskId, "tagId:", tagId, "userId:", userId);

  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    console.log("Server action: addTaskTag failed - unauthorized");
    return false;
  }

  const result = await addTagToTask(taskId, tagId, userId);

  // Only invalidate task-specific caches, not the main tags cache
  revalidateTag("tasks");

  console.log("Server action: addTaskTag returning:", result !== null);
  return result !== null;
}

export async function removeTaskTag(taskId: string, tagId: string, userId: string): Promise<boolean> {
  console.log("Server action: removeTaskTag called with taskId:", taskId, "tagId:", tagId, "userId:", userId);

  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    console.log("Server action: removeTaskTag failed - unauthorized");
    return false;
  }

  const result = await removeTagFromTask(taskId, tagId, userId);

  // Only invalidate task-specific caches, not the main tags cache
  revalidateTag("tasks");

  console.log("Server action: removeTaskTag returning:", result);
  return result;
}

export async function updateTaskTags(taskId: string, tagIds: string[], userId: string): Promise<boolean> {
  console.log("Server action: updateTaskTags called with taskId:", taskId, "tagIds:", tagIds, "userId:", userId);

  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    console.log("Server action: updateTaskTags failed - unauthorized");
    return false;
  }

  const result = await setTaskTags(taskId, tagIds, userId);

  // Only invalidate task-specific caches, not the main tags cache
  revalidateTag("tasks");

  console.log("Server action: updateTaskTags returning:", result);
  return result;
}

export async function fetchTaskById(taskId: string, userId: string): Promise<Task | null> {
  console.log("Server action: fetchTaskById called with taskId:", taskId, "userId:", userId);

  // Verify user authentication
  const user = await stackServerApp.getUser();
  if (!user || user.id !== userId) {
    console.log("Server action: fetchTaskById failed - unauthorized");
    return null;
  }

  try {
    const task = await getTaskById(taskId, userId);
    console.log("Server action: fetchTaskById returning:", task);
    return task;
  } catch (error) {
    console.error("Server action: fetchTaskById error:", error);
    return null;
  }
}
