"use server";

import { unstable_cache, revalidateTag } from "next/cache";
import { Space, getSpacesByUserId, getSpaceById, createSpace, updateSpace, deleteSpace, getListsBySpaceId } from "@/lib/db";

export const fetchSpaces = unstable_cache(
  async (userId: string): Promise<Space[]> => {
    console.log("Server action: fetchSpaces called with userId:", userId);
    const spaces = await getSpacesByUserId(userId);
    console.log("Server action: fetchSpaces returning:", spaces);
    return spaces;
  },
  ["spaces-jwt"],
  {
    tags: ["spaces"],
    revalidate: 600, // 10 minutes
  }
);

export const fetchSpace = unstable_cache(
  async (spaceId: string): Promise<Space | null> => {
    console.log("Server action: fetchSpace called with spaceId:", spaceId);
    const space = await getSpaceById(spaceId);
    console.log("Server action: fetchSpace returning:", space);
    return space;
  },
  ["space-jwt"],
  {
    tags: ["spaces"],
    revalidate: 600, // 10 minutes
  }
);

export async function addSpace(
  userId: string,
  name: string,
  description?: string | null,
  icon?: string | null
): Promise<Space | null> {
  console.log("Server action: addSpace called with userId:", userId, "name:", name);
  const space = await createSpace(userId, name, description, icon);

  // Invalidate cache when space is created
  revalidateTag("spaces");

  console.log("Server action: addSpace returning:", space);
  return space;
}

export async function editSpace(
  spaceId: string,
  data: { name?: string; description?: string | null; icon?: string | null }
): Promise<Space | null> {
  console.log("Server action: editSpace called with spaceId:", spaceId, "data:", data);

  const space = await updateSpace(spaceId, data);

  // Invalidate cache when space is updated
  revalidateTag("spaces");

  console.log("Server action: editSpace returning:", space);
  return space;
}

export async function removeSpace(spaceId: string, userId: string): Promise<boolean> {
  console.log("Server action: removeSpace called with spaceId:", spaceId, "userId:", userId);

  const success = await deleteSpace(spaceId, userId);

  // Invalidate cache when space is deleted
  revalidateTag("spaces");

  console.log("Server action: removeSpace returning:", success);
  return success;
}

export const fetchSpaceListCounts = unstable_cache(
  async (userId: string): Promise<Record<string, number>> => {
    console.log("Server action: fetchSpaceListCounts called with userId:", userId);
    
    try {
      const spaces = await getSpacesByUserId(userId);
      const counts: Record<string, number> = {};
      
      for (const space of spaces) {
        const lists = await getListsBySpaceId(userId, space.id);
        counts[space.id] = lists.length;
      }
      
      console.log("Server action: fetchSpaceListCounts returning:", counts);
      return counts;
    } catch (error) {
      console.error("Error fetching space list counts:", error);
      return {};
    }
  },
  ["space-list-counts-jwt"],
  {
    tags: ["spaces", "lists"],
    revalidate: 600, // 10 minutes
  }
);

export const fetchListsBySpace = unstable_cache(
  async (userId: string, spaceId: string) => {
    console.log("Server action: fetchListsBySpace called with userId:", userId, "spaceId:", spaceId);
    const lists = await getListsBySpaceId(userId, spaceId);
    console.log("Server action: fetchListsBySpace returning:", lists);
    return lists;
  },
  ["lists-by-space-jwt"],
  {
    tags: ["lists", "spaces"],
    revalidate: 600, // 10 minutes
  }
);
