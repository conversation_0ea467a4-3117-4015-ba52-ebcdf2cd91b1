"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Plus } from "lucide-react";
import { useAddCollaboratorMutation } from "@/lib/queries";

interface AddCollaboratorFormProps {
  spaceId: string;
  userId: string;
}

export function AddCollaboratorForm({ spaceId, userId }: AddCollaboratorFormProps) {
  const [email, setEmail] = useState("");
  const [error, setError] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  // TanStack Query mutation
  const addCollaboratorMutation = useAddCollaboratorMutation(spaceId, userId);

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const trimmedEmail = email.trim();
    
    if (!trimmedEmail) {
      setError("Email is required");
      return;
    }

    if (!validateEmail(trimmedEmail)) {
      setError("Please enter a valid email address");
      return;
    }

    setError("");

    try {
      await addCollaboratorMutation.mutateAsync({ email: trimmedEmail });
      setEmail("");
      inputRef.current?.focus();
    } catch (err: any) {
      console.error("Error adding collaborator:", err);
      if (err.message?.includes("Invalid email")) {
        setError("Please enter a valid email address");
      } else if (err.message?.includes("duplicate") || err.message?.includes("already")) {
        setError("This email is already invited to this space");
      } else {
        setError("Failed to add collaborator. Please try again.");
      }
    }
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (error) {
      setError("");
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-3">
      <div className="flex gap-2">
        <div className="flex-1">
          <Input
            ref={inputRef}
            type="email"
            placeholder="Enter email address"
            value={email}
            onChange={handleEmailChange}
            disabled={addCollaboratorMutation.isPending}
            className={error ? "border-destructive" : ""}
          />
          {error && (
            <p className="text-xs text-destructive mt-1">{error}</p>
          )}
        </div>
        <Button
          type="submit"
          disabled={!email.trim() || addCollaboratorMutation.isPending}
          className="px-3"
        >
          {addCollaboratorMutation.isPending ? (
            <LoadingSpinner size="xs" />
          ) : (
            <Plus className="h-4 w-4" />
          )}
          <span className="sr-only">Add collaborator</span>
        </Button>
      </div>
    </form>
  );
}
