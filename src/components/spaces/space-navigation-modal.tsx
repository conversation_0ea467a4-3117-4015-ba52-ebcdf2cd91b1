"use client";

import { useState } from "react";
import { useUser } from "@stackframe/stack";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ChevronLeft, Plus } from "lucide-react";
import { Space } from "@/lib/db";
import { useOwnedSpacesQuery, useSharedSpacesQuery, useSpaceListCountsQuery, useUserSettingsQuery, useJoinSpaceMutation } from "@/lib/queries";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useLongPress } from "@/hooks/use-long-press";
import { EditSpaceModal } from "./edit-space-modal";
import { CollaboratorAvatars } from "./collaborator-avatars";

interface SpaceNavigationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  currentSpaceId?: string | null;
  onSpaceSelect: (space: Space) => void;
  onCreateSpaceClick: () => void;
}

export function SpaceNavigationModal({
  open,
  onOpenChange,
  currentSpaceId,
  onSpaceSelect,
  onCreateSpaceClick,
}: SpaceNavigationModalProps) {
  const user = useUser();
  const [isLoading, setIsLoading] = useState(false);
  const [editSpaceModalOpen, setEditSpaceModalOpen] = useState(false);
  const [selectedSpace, setSelectedSpace] = useState<Space | null>(null);

  // TanStack Query hooks
  const { data: ownedSpaces = [], isLoading: ownedSpacesLoading } = useOwnedSpacesQuery(user?.id || "");
  const { data: sharedSpaces = [], isLoading: sharedSpacesLoading } = useSharedSpacesQuery(user?.id || "");
  // Temporarily disabled to fix infinite loop
  // const { data: spaceListCounts = {} } = useSpaceListCountsQuery(user?.id || "");
  const spaceListCounts = {};
  const { data: userSettings } = useUserSettingsQuery(user?.id || "");
  const joinSpaceMutation = useJoinSpaceMutation(user?.id || "");

  const spacesLoading = ownedSpacesLoading || sharedSpacesLoading;

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSpaceClick = async (space: Space) => {
    setIsLoading(true);

    // Check if this is a shared space that the user hasn't joined yet
    const isSharedSpace = sharedSpaces.some(s => s.id === space.id);
    const isOwnedSpace = ownedSpaces.some(s => s.id === space.id);

    if (isSharedSpace && !isOwnedSpace && user?.id) {
      try {
        // Mark the user as joined when they first visit the shared space
        await joinSpaceMutation.mutateAsync({ spaceId: space.id });
      } catch (error) {
        console.error("Error joining space:", error);
        // Continue with space selection even if join fails
      }
    }

    onSpaceSelect(space);
    // Close modal after selection
    setTimeout(() => {
      setIsLoading(false);
      onOpenChange(false);
    }, 100);
  };

  const handleCreateSpaceClick = () => {
    onCreateSpaceClick();
    onOpenChange(false);
  };

  const handleEditSpace = (space: Space) => {
    setSelectedSpace(space);
    setEditSpaceModalOpen(true);
  };

  const handleSpaceUpdated = (updatedSpace?: Space) => {
    // TanStack Query will automatically refetch and update the cache
    // No need to manually update state
  };

  const handleSpaceDeleted = () => {
    // TanStack Query will automatically refetch and update the cache
    // Close the edit modal
    setEditSpaceModalOpen(false);
    setSelectedSpace(null);
  };

  return (
    <>
    <MobileDialog open={open} onOpenChange={onOpenChange}>
      <MobileDialogContent className="sm:max-w-[425px]" fullHeight>
        <VisuallyHidden asChild>
          <MobileDialogTitle>Spaces</MobileDialogTitle>
        </VisuallyHidden>
        <MobileDialogHeader className="px-4 pt-4 pb-2">
          {/* Chevron on its own line */}
          <div className="flex items-start justify-start mb-3">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-6 w-6" />
              <span className="sr-only">Back</span>
            </Button>
          </div>

          {/* Header and Add Space button on the same line */}
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-medium">Spaces</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCreateSpaceClick}
              disabled={isLoading}
              className="h-8 px-2 text-xs"
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Space
            </Button>
          </div>
        </MobileDialogHeader>

        <div className="flex-1 overflow-y-auto px-4 pb-4">
          {spacesLoading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : (
            <div className="space-y-6">
              {/* Shared Spaces Section */}
              {sharedSpaces.length > 0 && (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground px-1">Shared Spaces</h3>
                  {sharedSpaces.map((space) => {
                    const listCount = spaceListCounts[space.id] || 0;
                    const isActive = space.id === currentSpaceId;

                    // Create SpaceItem component to handle long press
                    const SharedSpaceItem = () => {
                      const { cleanup, ...longPressHandlers } = useLongPress({
                        onLongPress: () => {}, // Shared spaces can't be edited by non-owners
                        onClick: () => handleSpaceClick(space),
                        delay: 500,
                        threshold: 10,
                      });

                      return (
                        <button
                          key={space.id}
                          type="button"
                          disabled={isLoading}
                          data-selected={isActive}
                          className={`w-full flex items-center justify-between p-4 rounded-lg border transition-colors text-left glass-card ${
                            isActive
                              ? "border-primary/30"
                              : "border-border hover:bg-muted/50"
                          } ${isLoading ? "opacity-50 cursor-not-allowed" : ""}`}
                          {...longPressHandlers}
                        >
                          <div className="flex items-center gap-3 min-w-0 flex-1">
                            <div className="text-2xl flex-shrink-0">
                              {space.icon || "📋"}
                            </div>
                            <div className="min-w-0 flex-1">
                              <div className={`font-medium truncate ${isActive ? "font-semibold" : ""}`}>
                                {space.name}
                              </div>
                              {space.description && (
                                <div className="text-sm text-muted-foreground truncate">
                                  {space.description}
                                </div>
                              )}
                              <div className="text-xs text-muted-foreground">
                                Shared
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2 flex-shrink-0">
                            <CollaboratorAvatars spaceId={space.id} maxAvatars={2} size="sm" />
                            <div className="text-sm text-muted-foreground">
                              {listCount} {listCount === 1 ? "list" : "lists"}
                            </div>
                            {isActive && (
                              <div className="w-2 h-2 bg-primary rounded-full" />
                            )}
                          </div>
                        </button>
                      );
                    };

                    return <SharedSpaceItem key={space.id} />;
                  })}
                </div>
              )}

              {/* Owned Spaces Section */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-muted-foreground px-1">Spaces</h3>
                {ownedSpaces.map((space) => {
                const listCount = spaceListCounts[space.id] || 0;
                const isActive = space.id === currentSpaceId;
                const isDefault = userSettings?.default_space_id === space.id;

                // Create SpaceItem component to handle long press
                const SpaceItem = () => {
                  const { cleanup, ...longPressHandlers } = useLongPress({
                    onLongPress: () => handleEditSpace(space),
                    onClick: () => handleSpaceClick(space),
                    delay: 500,
                    threshold: 10,
                  });

                  return (
                    <button
                      key={space.id}
                      type="button"
                      disabled={isLoading}
                      data-selected={isActive}
                      className={`w-full flex items-center justify-between p-4 rounded-lg border transition-colors text-left glass-card ${
                        isActive
                          ? "border-primary/30"
                          : "border-border hover:bg-muted/50"
                      } ${isLoading ? "opacity-50 cursor-not-allowed" : ""}`}
                      {...longPressHandlers}
                    >
                      <div className="flex items-center gap-3 min-w-0 flex-1">
                        <div className="text-2xl flex-shrink-0">
                          {space.icon || "📋"}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className={`font-medium truncate ${isActive ? "font-semibold" : ""}`}>
                            {space.name}
                          </div>
                          {space.description && (
                            <div className="text-sm text-muted-foreground truncate">
                              {space.description}
                            </div>
                          )}
                          {isDefault && (
                            <div className="text-xs text-muted-foreground">
                              Default
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2 flex-shrink-0">
                        <div className="text-sm text-muted-foreground">
                          {listCount} {listCount === 1 ? "list" : "lists"}
                        </div>
                        {isActive && (
                          <div className="w-2 h-2 bg-primary rounded-full" />
                        )}
                      </div>
                    </button>
                  );
                };

                return <SpaceItem key={space.id} />;
              })}

              {/* Create New Space Button */}
              <Card
                className="border-2 border-muted-foreground/20 bg-muted/10 hover:bg-muted/20 transition-colors cursor-pointer"
                onClick={handleCreateSpaceClick}
              >
                <CardContent className="px-2 flex items-center justify-center">
                  <Plus className="h-5 w-5 text-muted-foreground" />
                </CardContent>
              </Card>
              </div>
            </div>
          )}
        </div>
      </MobileDialogContent>
    </MobileDialog>

    {/* Edit Space Modal */}
    <EditSpaceModal
      open={editSpaceModalOpen}
      onOpenChange={setEditSpaceModalOpen}
      space={selectedSpace}
      onSpaceUpdated={handleSpaceUpdated}
      onSpaceDeleted={handleSpaceDeleted}
    />
  </>
  );
}
