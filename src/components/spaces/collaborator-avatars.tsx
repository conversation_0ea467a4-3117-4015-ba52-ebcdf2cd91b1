"use client";

import React, { useMemo } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { SpaceCollaborator } from "@/lib/db";
import { useSpaceCollaboratorsQuery } from "@/lib/queries";

interface CollaboratorAvatarsProps {
  spaceId: string;
  maxAvatars?: number;
  size?: "sm" | "md" | "lg";
}

const CollaboratorAvatars = React.memo(function CollaboratorAvatars({
  spaceId,
  maxAvatars = 3,
  size = "sm"
}: CollaboratorAvatarsProps) {
  // Only fetch if spaceId is valid and not empty
  const { data: collaborators = [] } = useSpaceCollaboratorsQuery(spaceId);

  // Filter to only show joined collaborators (those with user_id and joined_at)
  const joinedCollaborators = useMemo(() => {
    return collaborators.filter(collaborator => 
      collaborator.user_id && collaborator.joined_at
    );
  }, [collaborators]);

  const sizeClasses = {
    sm: "h-6 w-6 text-xs",
    md: "h-8 w-8 text-sm", 
    lg: "h-10 w-10 text-base"
  };

  const overlapClasses = {
    sm: "-ml-2 first:ml-0",
    md: "-ml-3 first:ml-0",
    lg: "-ml-4 first:ml-0"
  };

  if (joinedCollaborators.length === 0) {
    return null;
  }

  const displayedCollaborators = joinedCollaborators.slice(0, maxAvatars);
  const remainingCount = joinedCollaborators.length - maxAvatars;

  const getInitials = (email: string) => {
    const parts = email.split('@')[0].split('.');
    if (parts.length >= 2) {
      return (parts[0][0] + parts[1][0]).toUpperCase();
    }
    return email.slice(0, 2).toUpperCase();
  };

  return (
    <div className="flex items-center">
      {displayedCollaborators.map((collaborator, index) => (
        <Avatar 
          key={collaborator.id} 
          className={`${sizeClasses[size]} ${overlapClasses[size]} border-2 border-background`}
        >
          <AvatarImage src="" alt={collaborator.email} />
          <AvatarFallback className="bg-muted text-muted-foreground">
            {getInitials(collaborator.email)}
          </AvatarFallback>
        </Avatar>
      ))}
      
      {remainingCount > 0 && (
        <div 
          className={`${sizeClasses[size]} ${overlapClasses[size]} border-2 border-background rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium`}
        >
          +{remainingCount}
        </div>
      )}
    </div>
  );
});

export { CollaboratorAvatars };
