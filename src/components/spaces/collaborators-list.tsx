"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Trash2, User, UserCheck } from "lucide-react";
import { SpaceCollaborator } from "@/lib/db";
import { useSpaceCollaboratorsQuery, useRemoveCollaboratorMutation } from "@/lib/queries";

interface CollaboratorsListProps {
  spaceId: string;
  userId: string;
  isOwner: boolean;
}

export function CollaboratorsList({ spaceId, userId, isOwner }: CollaboratorsListProps) {
  const [removingId, setRemovingId] = useState<string | null>(null);

  // TanStack Query hooks - temporarily disabled to fix infinite loop
  // const { data: collaborators = [], isLoading } = useSpaceCollaboratorsQuery(spaceId);
  const collaborators: any[] = [];
  const isLoading = false;
  const removeCollaboratorMutation = useRemoveCollaboratorMutation(spaceId, userId);

  const handleRemoveCollaborator = async (collaboratorId: string) => {
    if (!isOwner) return;
    
    setRemovingId(collaboratorId);
    try {
      await removeCollaboratorMutation.mutateAsync({ collaboratorId });
    } catch (error) {
      console.error("Error removing collaborator:", error);
    } finally {
      setRemovingId(null);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-4">
        <LoadingSpinner size="sm" />
      </div>
    );
  }

  if (collaborators.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-sm text-muted-foreground">No collaborators yet</p>
        <p className="text-xs text-muted-foreground mt-1">
          Add collaborators to share this space
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {collaborators.map((collaborator) => {
        const isJoined = !!collaborator.joined_at;
        const isRemoving = removingId === collaborator.id;

        return (
          <div
            key={collaborator.id}
            className="flex items-center justify-between p-3 rounded-lg border bg-card"
          >
            <div className="flex items-center gap-3 min-w-0 flex-1">
              <div className="flex-shrink-0">
                {isJoined ? (
                  <UserCheck className="h-4 w-4 text-green-600" />
                ) : (
                  <User className="h-4 w-4 text-muted-foreground" />
                )}
              </div>
              {isJoined && (
                <Avatar className="h-6 w-6 flex-shrink-0">
                  <AvatarImage src={collaborator.user?.avatar_url || ""} alt={collaborator.user?.name || collaborator.email} />
                  <AvatarFallback className="text-xs">
                    {collaborator.user?.name?.charAt(0) || collaborator.email.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              )}
              <div className="min-w-0 flex-1">
                <div className="text-sm font-medium truncate">
                  {collaborator.email}
                </div>
                <div className="text-xs text-muted-foreground">
                  {isJoined ? "Joined" : "Pending"}
                </div>
              </div>
            </div>

            {isOwner && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleRemoveCollaborator(collaborator.id)}
                disabled={isRemoving || removeCollaboratorMutation.isPending}
                className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
              >
                {isRemoving ? (
                  <LoadingSpinner size="xs" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                <span className="sr-only">Remove collaborator</span>
              </Button>
            )}
          </div>
        );
      })}
    </div>
  );
}
