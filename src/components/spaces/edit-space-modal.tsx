"use client";

import { useState, useRef, useEffect } from "react";
import { useUser } from "@stackframe/stack";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { RichTextEditor } from "@/components/ui/rich-text-editor";
import {
  MobileDialog,
  MobileDialogContent,
  MobileDialogHeader,
  MobileDialogTitle,
} from "@/components/ui/mobile-dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { ChevronLeft, Search } from "lucide-react";
import { Space, UserSettings } from "@/lib/db";
import { useEditSpaceMutation, useDeleteSpaceMutation, useUpdateSettingsMutation, useUserSettingsQuery } from "@/lib/queries";

// Common emoji icons for spaces
const SPACE_ICONS = [
  "📋", "📝", "📁", "📂", "🗂️", "📊", "📈", "📉", "📌", "📍",
  "🎯", "🎪", "🎨", "🎭", "🎲", "🎮", "💼", "💻", "💡", "💰",
  "💎", "💳", "💸", "💵", "💴", "💶", "🏠", "🏢", "🏪", "🏫",
  "🏬", "🏭", "🏮", "🏯", "🏰", "🏳️", "🔥", "🔔", "🔕", "🔖",
  "🔗", "🔘", "🔙", "🔚", "🔛", "🔜", "⭐", "⚡", "⚽", "⚾",
  "⛄", "⛅", "⛈️", "⛎", "⛏️", "⛑️", "🌟", "🌠", "🌡️", "🌢",
  "🌣", "🌤️", "🌥️", "🌦️", "🌧️", "🌨️", "🚀", "🚁", "🚂", "🚃",
  "🚄", "🚅", "🚆", "🚇", "🚈", "🚉"
];

interface EditSpaceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  space: Space | null;
  onSpaceUpdated: (updatedSpace?: Space) => void;
  onSpaceDeleted: () => void;
}

export function EditSpaceModal({
  open,
  onOpenChange,
  space,
  onSpaceUpdated,
  onSpaceDeleted,
}: EditSpaceModalProps) {
  const user = useUser();
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [selectedIcon, setSelectedIcon] = useState<string>("📋");
  const [iconSearch, setIconSearch] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Auto-save refs
  const nameTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const descriptionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // TanStack Query mutations
  const editSpaceMutation = useEditSpaceMutation(user?.id || "");
  const deleteSpaceMutation = useDeleteSpaceMutation(user?.id || "");
  const updateSettingsMutation = useUpdateSettingsMutation(user?.id || "");
  const { data: userSettings } = useUserSettingsQuery(user?.id || "");

  // Initialize form data when space changes
  useEffect(() => {
    if (space) {
      setName(space.name);
      setDescription(space.description || "");
      setSelectedIcon(space.icon || "📋");
      setIconSearch("");
      setError("");
    }
  }, [space]);

  // Filter icons based on search
  const filteredIcons = SPACE_ICONS.filter(icon =>
    iconSearch === "" || icon.includes(iconSearch)
  );

  const handleClose = () => {
    // Clear any pending timeouts
    if (nameTimeoutRef.current) {
      clearTimeout(nameTimeoutRef.current);
    }
    if (descriptionTimeoutRef.current) {
      clearTimeout(descriptionTimeoutRef.current);
    }
    
    onOpenChange(false);
  };

  const handleNameChange = (value: string) => {
    setName(value);
    setError("");
  };

  const handleDescriptionChange = (value: string) => {
    setDescription(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      setError("Space name is required");
      return;
    }

    if (!user || !space) {
      setError("You must be logged in to edit a space");
      return;
    }

    setIsSubmitting(true);
    setError("");

    try {
      const result = await editSpaceMutation.mutateAsync({
        spaceId: space.id,
        data: {
          name: name.trim(),
          description: description.trim() || null,
          icon: selectedIcon,
        }
      });

      if (result) {
        onSpaceUpdated(result);
        handleClose();
      } else {
        setError("Failed to update space. Please try again.");
      }
    } catch (err) {
      console.error("Error updating space:", err);
      setError("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    if (!user || !space) return;

    setIsSubmitting(true);
    try {
      const success = await deleteSpaceMutation.mutateAsync(space.id);
      if (success) {
        onSpaceDeleted();
        handleClose();
      } else {
        setError("Failed to delete space. Please try again.");
      }
    } catch (err) {
      console.error("Error deleting space:", err);
      setError("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
      setShowDeleteDialog(false);
    }
  };

  const handleSetAsDefault = async () => {
    if (!user || !space) return;

    setIsSubmitting(true);
    try {
      await updateSettingsMutation.mutateAsync({
        default_space_id: space.id,
      });
      
      onSpaceUpdated(); // Refresh the spaces list to show updated default
      handleClose();
    } catch (err) {
      console.error("Error setting default space:", err);
      setError("An error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const isDefaultSpace = userSettings?.default_space_id === space?.id;

  if (!space) return null;

  return (
    <>
      <MobileDialog open={open} onOpenChange={onOpenChange}>
        <MobileDialogContent className="sm:max-w-[425px]" fullHeight>
          <VisuallyHidden asChild>
            <MobileDialogTitle>Edit Space</MobileDialogTitle>
          </VisuallyHidden>
          <MobileDialogHeader className="flex items-start justify-start px-4 pt-4 pb-0">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-6 w-6" />
              <span className="sr-only">Back</span>
            </Button>
          </MobileDialogHeader>

          <form onSubmit={handleSubmit} className="flex-1 flex flex-col">
            <div className="flex-1 overflow-y-auto px-4 pb-4">
              <div className="space-y-4">
                {/* Space Name */}
                <div className="space-y-2">
                  <Input
                    id="name"
                    type="search"
                    value={name}
                    onChange={(e) => handleNameChange(e.target.value)}
                    placeholder="Space name"
                    autoComplete="off"
                    spellCheck="false"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Space Description */}
                <div className="space-y-2">
                  <RichTextEditor
                    id="description"
                    value={description}
                    onChange={handleDescriptionChange}
                    placeholder="Add a description for this space (optional)"
                    disabled={isSubmitting}
                  />
                </div>

                {/* Icon Selection */}
                <div className="space-y-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                    <Input
                      type="search"
                      placeholder="Search icons..."
                      value={iconSearch}
                      onChange={(e) => setIconSearch(e.target.value)}
                      className="pl-10"
                      autoComplete="off"
                      spellCheck="false"
                      disabled={isSubmitting}
                    />
                  </div>
                  
                  <div className="grid grid-cols-8 gap-2 max-h-32 overflow-y-auto">
                    {filteredIcons.map((icon) => (
                      <button
                        key={icon}
                        type="button"
                        onClick={() => setSelectedIcon(icon)}
                        disabled={isSubmitting}
                        className={`p-2 text-xl rounded-md border transition-colors ${
                          selectedIcon === icon
                            ? "border-primary bg-primary/10"
                            : "border-border hover:bg-muted/50"
                        }`}
                      >
                        {icon}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Error Message */}
                {error && (
                  <div className="text-sm text-destructive">
                    {error}
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between px-4 pb-4 pt-2 border-t">
              <Button
                type="button"
                variant="destructive"
                onClick={() => setShowDeleteDialog(true)}
                disabled={isSubmitting}
                className="h-10"
              >
                Delete Space
              </Button>
              
              <Button
                type="button"
                onClick={handleSetAsDefault}
                disabled={isSubmitting || isDefaultSpace}
                className="h-10"
              >
                {isDefaultSpace ? "Default Space" : "Set as Default"}
              </Button>
            </div>
          </form>
        </MobileDialogContent>
      </MobileDialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogTitle>Delete Space</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete "{space?.name}"? This will also delete all lists and tasks in this space.
            <br />
            <br />
            This action cannot be undone.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isSubmitting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete Space
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
